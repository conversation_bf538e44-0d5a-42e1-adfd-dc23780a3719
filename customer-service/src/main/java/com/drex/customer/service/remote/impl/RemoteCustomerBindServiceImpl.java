package com.drex.customer.service.remote.impl;

import com.drex.customer.api.RemoteCustomerBindService;
import com.drex.customer.api.request.BindSocialInfoRequest;
import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.api.response.CustomerBindDTO;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.mapstruct.CustomerBindMapperStruct;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 10:08
 * @description:
 */
@DubboService
public class RemoteCustomerBindServiceImpl implements RemoteCustomerBindService {

    @Resource
    private CustomerBindService customerBindService;
    @Resource
    private CustomerBindMapperStruct customerBindMapperStruct;

    @Override
    public boolean bindSocial(BindSocialInfoRequest bindSocialInfoRequest) {
        CustomerBind customerBind = new CustomerBind();
        customerBind.setCustomerId(bindSocialInfoRequest.getCustomerId());
        customerBind.setSocialPlatform(bindSocialInfoRequest.getPlatform());
        customerBind.setSocialUserId(bindSocialInfoRequest.getSocialId());
        customerBind.setSocialHandleName(bindSocialInfoRequest.getSocialHandleName());
        customerBind.setSocialProfileImage(bindSocialInfoRequest.getSocialProfileImage());
        customerBind.setSocialEmail(bindSocialInfoRequest.getSocialEmail());
        customerBind.setCreated(System.currentTimeMillis());
        return customerBindService.insert(customerBind);
    }

    @Override
    public boolean unbindSocial(String customerId, String socialPlatform) {
        CustomerBind customerBind = new CustomerBind();
        customerBind.setCustomerId(customerId);
        customerBind.setSocialPlatform(socialPlatform);
        return customerBindService.delete(customerBind);
    }

    @Override
    public boolean reservePrivacyAuth(String customerId, String socialPlatform) {
        return customerBindService.reservePrivacyAuth(customerId, socialPlatform);
    }

    @Override
    public CustomerBindDTO queryBySocialInfo(CustomerBindQuery request) {
        CustomerBind customerBind = customerBindService.findBySocialInfo(request);
        return customerBindMapperStruct.toCustomerBindDTO(customerBind);
    }

    @Override
    public CustomerBindDTO findByCustomerId(String customerId, String socialPlatform) {
        CustomerBind customerBind = customerBindService.findByCustomerId(customerId, socialPlatform);
        return customerBindMapperStruct.toCustomerBindDTO(customerBind);
    }


}
