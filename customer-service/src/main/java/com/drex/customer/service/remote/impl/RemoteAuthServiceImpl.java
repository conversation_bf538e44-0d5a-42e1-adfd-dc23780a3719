package com.drex.customer.service.remote.impl;

import co.evg.scaffold.event.client.EventClient;
import co.evg.scaffold.event.client.EventDTO;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.drex.customer.api.ErrorCode;
import com.drex.customer.api.RemoteAuthService;
import com.drex.customer.api.constants.CustomerConstants;
import com.drex.customer.api.request.AuthLoginRequest;
import com.drex.customer.api.request.BindWalletRequest;
import com.drex.customer.api.request.CustomerBindQuery;
import com.drex.customer.api.request.ThirdAuthRequest;
import com.drex.customer.api.response.CustomerCodesDTO;
import com.drex.customer.api.response.PassportConnectDTO;
import com.drex.customer.api.response.PassportDTO;
import com.drex.customer.api.response.ThirdAuthDTO;
import com.drex.customer.api.response.ThirdBindingsDTO;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.PassportService;
import com.drex.customer.service.business.AuthService;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.business.ThirdWebService;
import com.drex.customer.service.code.client.CustomerCodeServiceClient;
import com.drex.customer.service.config.CustomerProperties;
import com.drex.customer.service.mapstruct.AuthMapperStruct;
import com.drex.customer.api.constants.WalletConstant;
import com.drex.customer.service.socialPlatform.model.Token;
import com.drex.customer.service.socialPlatform.service.SocialPlatformServiceFactory;
import com.drex.model.CustomerException;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ons.OnsProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@DubboService
@Slf4j
public class RemoteAuthServiceImpl implements RemoteAuthService {

    @Resource
    private AuthService authService;
    @Resource
    private AuthMapperStruct authMapperStruct;
    @Resource
    private CustomerBindService customerBindService;
    @Resource
    private ThirdWebService thirdWebService;
    @Resource
    private SocialPlatformServiceFactory platformServiceFactory;
    @Resource
    private CustomerCodeServiceClient customerCodeServiceClient;
    @Resource
    private PassportService passportService;
    @Resource
    private EventClient eventClient;
    @Resource
    private CustomerProperties customerProperties;
    @Resource
    private OnsProducer onsProducer;
    @Resource
    private OnsProperties onsProperties;


    @Override
    public Response<PassportDTO> login(AuthLoginRequest request) {
        try {
            if (StringUtils.isEmpty(request.getWalletAddress())) {
                return Response.error(ErrorCode.LOGIN_FAIL.getCode(), "walletAddress is required");
            }
            if (request.getSubConnectProvider() == null) {
                // 兼容 2B 无 subConnectProvider的情况：按地址查询所有connect，若存在Key钱包，则取任意一个subConnectProvider
                List<PassportConnectDTO> passportConnectByAddress = passportService.getPassportConnectByAddress(request.getWalletAddress());
                if (!CollectionUtils.isEmpty(passportConnectByAddress)) {
                    passportConnectByAddress.stream().filter(connect ->
                            WalletConstant.ConnectTypeEnum.KEY.getCode().equalsIgnoreCase(connect.getConnectType())
                    ).findFirst().ifPresent(connect -> {
                        log.info("2B login, match subConnectProvider:{}", connect.getSubConnectProvider());
                        request.setSubConnectProvider(WalletConstant.PlatformEnum.getEnumByCode(connect.getSubConnectProvider()));
                    });
                }
            }
            log.info("login request:{}", request);

            PassportDTO passportDTO = passportService.getPassportByWalletAddress(request.getWalletAddress(), request.getSubConnectProvider());

            // 如果passport不存在 且是 ThirdWeb社媒登录，则尝试执行社媒钱包合并逻辑
            if (passportDTO == null && request.getSubConnectProvider() != null && request.getSubConnectProvider() != WalletConstant.PlatformEnum.Wallet) {
                passportDTO = handleSocialEOAMerge(request);
            }

            boolean checkCode = true;
            if (passportDTO == null) {
                if("-1".equals(request.getCode())){
                    return Response.error(ErrorCode.LOGIN_FAIL.getCode(), "code is invalid");
                }
                if(StringUtils.isNotBlank(request.getCode())) {
                    try{
                        //核销二维码
                        checkCode = customerCodeServiceClient.checkCode(request.getCode(), new String[]{CustomerConstants.SceneEnum.register.name()},  null);
                    } catch (Exception e) {
                        log.error("login ", e);
                        checkCode = false;
                    }
                }
                // Create new customer
                if(!checkCode){
                    return Response.error(ErrorCode.LOGIN_FAIL.getCode(), "code is invalid");
                }
                passportDTO = register(request);
                if(passportDTO == null){
                    return Response.error(ErrorCode.LOGIN_FAIL.getCode(), "register fail");
                }
                passportDTO.setIsNewUser(true);
            }else{
                if(StringUtils.isNotBlank(request.getCode()) && !"-1".equals(request.getCode())){
                    try{
                        //核销二维码
                        checkCode = customerCodeServiceClient.checkCode(request.getCode(), new String[]{CustomerConstants.SceneEnum.register.name()}, passportDTO.getPassportId());
                    } catch (Exception e) {
                        log.error("login ", e);
                        checkCode = false;
                    }
                }
            }
            if(checkCode && StringUtils.isNotBlank(request.getCode()) && !"-1".equals(request.getCode())){
                CustomerCodesDTO customerCodesDTO = customerCodeServiceClient.useCode(passportDTO, request.getCode(), new String[]{CustomerConstants.SceneEnum.register.name()}, null);
                //发送event消息
                if(customerCodesDTO != null){
                    passportDTO.setIsRedeemedSuccessfully(true);
                    sendBadgeEvent(passportDTO.getPassportId(), customerCodesDTO.getScene(), CustomerConstants.SERIES_TREX_JOURNEY, new HashMap<>());
                }
            }
            // Create response with code, message and data structure
            Response<PassportDTO> response = Response.success(passportDTO);
            response.setMessage("success");

            return response;
        } catch (Exception e) {
            log.error("login error", e);
            return Response.error(ErrorCode.UNKNOWN_ERROR.getCode(), ErrorCode.UNKNOWN_ERROR.getMessage());
        }
    }

    // 社媒钱包合并逻辑
    private PassportDTO handleSocialEOAMerge(AuthLoginRequest request) {
        // 1. 检查该社交账号是否已绑定到ThirdWeb中的EOA地址
        ThirdWebService.ThirdWebUserAccount thirdWebUserAccount =
                thirdWebService.getThirdWebUserAccount(
                        request.getWalletAddress(),
                        request.getSubConnectProvider()
                );
        if (thirdWebUserAccount == null) {
            log.info("thirdweb user not found, walletAddress:{}, subConnectProvider:{}", request.getWalletAddress(), request.getSubConnectProvider());
            return null;
        }

        // 2. 检查该社交账号是否已绑定到系统
        CustomerBindQuery query = CustomerBindQuery.builder()
                .socialPlatform(request.getSubConnectProvider().getCode())
                .socialUserId(thirdWebUserAccount.getId())
                .build();
        CustomerBind bindInfo = customerBindService.findBySocialInfo(query);
        if (bindInfo == null) {
            log.info("customer bind not found, socialPlatform:{}, socialUserId:{}", request.getSubConnectProvider(), thirdWebUserAccount.getId());
            return null;
        }

        // 3. 检查该EOA地址是否已经是某个passport的KEY
        List<PassportConnectDTO> passportConnectByAddress =
                passportService.getPassportConnectByAddress(thirdWebUserAccount.getAddress());
        if (CollectionUtils.isEmpty(passportConnectByAddress)) {
            log.info("passport connect not found, walletAddress:{}", thirdWebUserAccount.getAddress());
            return null;
        }

        // 4. 检查是否存在KEY类型的连接
        boolean hasKey = passportConnectByAddress.stream()
                .anyMatch(connect ->
                        WalletConstant.ConnectTypeEnum.KEY.getCode().equalsIgnoreCase(connect.getConnectType())
                );
        if (!hasKey) {
            log.info("no key connect found, walletAddress:{}", thirdWebUserAccount.getAddress());
            return null;
        }

        // 5. 将该EOA地址+社媒类型绑定为新的KEY
        log.info("begin to handle social eoa merge, walletAddress: {}, subConnectProvider: {}", request.getWalletAddress(), request.getSubConnectProvider());
        BindWalletRequest bindRequest = BindWalletRequest.builder()
                .walletAddress(thirdWebUserAccount.getAddress())
                .walletProvider(WalletConstant.WalletProviderEnum.ThirdWeb)
                .subConnectProvider(request.getSubConnectProvider())
                .connectType(WalletConstant.ConnectTypeEnum.KEY)
                .walletType(WalletConstant.WalletTypeEnum.SOCIAL)
                .accountDetail(thirdWebUserAccount.getDetails())
                .build();
        Boolean b = passportService.bindWalletAddress(bindRequest);
        log.info("handle social eoa merge result: {}", b);

        return passportService.getPassportByWalletAddress(request.getWalletAddress(), request.getSubConnectProvider());
    }

    @Override
    public Response<ThirdAuthDTO> thirdAuth(ThirdAuthRequest request) {
        try {
            Token token = authService.auth(request.getPlatform(), request.getCode(), request.getCustomerId());
            ThirdAuthDTO thirdAuthResponse = authMapperStruct.toThirdAuthDTO(token);
            return Response.success(thirdAuthResponse);
        } catch (CustomerException e) {
            return Response.error(e.getCode().getCode(), e.getCode().getMessage());
        }
    }

    @Override
    public Response<List<ThirdBindingsDTO>> thirdBindings(String customerId) {
        log.info("[RemoteAuthService] thirdBindings customerId: {}", customerId);
        // 获取支持的社交平台列表
        List<String> socialPlatforms = platformServiceFactory.getSupportedPlatforms();

        // 查询用户已绑定的社交平台
        List<CustomerBind> customerBinds = customerBindService.findByCustomerId(customerId, socialPlatforms);

        // 创建结果列表
        List<ThirdBindingsDTO> result = new ArrayList<>();

        // 处理每个社交平台
        for (String platform : socialPlatforms) {
            ThirdBindingsDTO bindingsDTO = new ThirdBindingsDTO();
            bindingsDTO.setPlatform(platform);

            // 查找当前平台是否已绑定
            CustomerBind existingBind = customerBinds.stream()
                    .filter(bind -> platform.equals(bind.getSocialPlatform()))
                    .findFirst()
                    .orElse(null);

            if (existingBind != null) {
                // 已绑定
                bindingsDTO.setStatus("1");
                bindingsDTO.setConnectUrl(null);
                bindingsDTO.setSocialHandleName(existingBind.getSocialHandleName());
                bindingsDTO.setSocialProfileImage(existingBind.getSocialProfileImage());
            } else {
                // 未绑定
                bindingsDTO.setStatus("0");
                // Apple平台特殊处理：未绑定时不展示
                if (WalletConstant.PlatformEnum.Apple.name().equalsIgnoreCase(platform)) {
                    continue;
                }
            }
            result.add(bindingsDTO);
        }
        return Response.success(result);
    }

    private PassportDTO register(AuthLoginRequest request) {
        PassportDTO passport = passportService.createPassport(request);
        if(passport != null){
            Map<String, String> body = new HashMap<>();
            body.put("event", "register");
            body.put("data", JSON.toJSONString(passport));
            onsProducer.send(customerProperties.getCustomerRegisterTopic()+onsProperties.getEnv(), JSON.toJSONString(body));

            // 发送早鸟徽章
            body.put("progressValue", passport.getCreatedAt().getTime() + "");
            sendBadgeEvent(passport.getPassportId(), CustomerConstants.SCENE_TREX_OG, CustomerConstants.SERIES_TREX_JOURNEY, body);
        }
        return passport;
    }

    private void sendBadgeEvent(String customerId, String scene, String series, Map<String, String> params) {
        JSONObject body = new JSONObject();
        if (params != null) {
            body.putAll(params);
        }
        body.put("customerId", customerId);
        body.put("contentId", customerId + "_" + scene);
        body.put("series", series);
        body.put("scene", scene);

        EventDTO event = EventDTO.builder()
                .name("claimable_badge")
                .time(System.currentTimeMillis())
                .customerId(customerId)
                .globalUid(body.getString("contentId"))
                .source(eventClient.getDefaultSource())
                .body(body)
                .build();
        eventClient.asyncPush(event);
    }
}
